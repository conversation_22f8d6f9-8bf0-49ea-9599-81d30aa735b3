// 管理员相关路由 - 使用 OpenAPI 代码内文档
import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Env, Admin, AdminLoginResponse, JWTPayload } from '../types';
import { verifyPassword, generateJWT, getJWTSecret, verifyJWT } from '../utils/crypto';

// 创建 OpenAPIHono 应用实例
const admin = new OpenAPIHono<{ Bindings: Env }>();

// 管理员登录请求 Schema
const AdminLoginRequestSchema = z.object({
  username: z.string().min(1).openapi({
    description: '用户名',
    example: 'root'
  }),
  password: z.string().min(1).openapi({
    description: '密码',
    example: 'password'
  })
}).openapi('AdminLoginRequest');

// 管理员登录响应 Schema
const AdminLoginResponseSchema = z.object({
  token: z.string().openapi({
    description: 'JWT访问令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  }),
  admin_id: z.string().openapi({
    description: '管理员ID',
    example: 'adm_123456789'
  }),
  username: z.string().openapi({
    description: '用户名',
    example: 'root'
  }),
  role: z.enum(['super', 'normal']).openapi({
    description: '角色：super=超级管理员, normal=普通管理员',
    example: 'super'
  }),
  authorized_products: z.array(z.string()).optional().openapi({
    description: '授权产品列表（普通管理员）',
    example: ['prod_123', 'prod_456']
  }),
  expires_in: z.number().openapi({
    description: '令牌有效期（秒）',
    example: 86400
  })
}).openapi('AdminLoginResponse');

// 管理员登录路由定义
const loginRoute = createRoute({
  method: 'post',
  path: '/login',
  tags: ['管理员认证'],
  summary: '管理员登录',
  description: '管理员使用用户名和密码登录，获取JWT访问令牌',
  request: {
    body: {
      content: {
        'application/json': {
          schema: AdminLoginRequestSchema
        }
      }
    }
  },
  responses: {
    200: {
      description: '登录成功',
      content: {
        'application/json': {
          schema: AdminLoginResponseSchema
        }
      }
    },
    401: {
      description: '用户名或密码错误',
      content: {
        'application/json': {
          schema: z.object({
            error: z.string().openapi({
              description: '错误信息',
              example: '用户名或密码错误'
            })
          })
        }
      }
    },
    500: {
      description: '服务器内部错误',
      content: {
        'application/json': {
          schema: z.object({
            error: z.string().openapi({
              description: '错误信息',
              example: '登录失败，请稍后重试'
            })
          })
        }
      }
    }
  }
});

// 实现管理员登录接口
admin.openapi(loginRoute, async (c) => {
  try {
    const { username, password } = c.req.valid('json');

    // 查询管理员信息
    const adminQuery = await c.env.DB
      .prepare('SELECT * FROM admins WHERE username = ? AND status = ?')
      .bind(username, 'active')
      .first<Admin>();

    if (!adminQuery) {
      return c.json({
        error: '用户名或密码错误'
      }, 401);
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, adminQuery.password_hash);
    if (!isPasswordValid) {
      return c.json({
        error: '用户名或密码错误'
      }, 401);
    }

    // 解析授权产品列表
    let authorizedProducts: string[] = [];
    if (adminQuery.authorized_products) {
      try {
        authorizedProducts = JSON.parse(adminQuery.authorized_products);
      } catch (error) {
        console.error('解析授权产品列表失败:', error);
      }
    }

    // 生成JWT Token
    const jwtSecret = getJWTSecret(c.env);
    const tokenPayload: JWTPayload = {
      admin_id: adminQuery.id,
      username: adminQuery.username,
      role: adminQuery.role,
      authorized_products: authorizedProducts,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 86400 // 24小时
    };

    const token = await generateJWT(tokenPayload, jwtSecret, 86400);

    // 更新最后登录时间
    await c.env.DB
      .prepare('UPDATE admins SET last_login = CURRENT_TIMESTAMP WHERE id = ?')
      .bind(adminQuery.id)
      .run();

    // 缓存管理员权限信息到KV
    const cacheKey = `admin:auth:${adminQuery.id}`;
    const cacheData = {
      username: adminQuery.username,
      role: adminQuery.role,
      authorized_products: authorizedProducts,
      status: adminQuery.status,
      last_updated: new Date().toISOString()
    };

    await c.env.VERIFY_CACHE.put(cacheKey, JSON.stringify(cacheData), {
      expirationTtl: 1800 // 30分钟
    });

    // 直接返回标准响应格式
    return c.json({
      token,
      admin_id: adminQuery.id,
      username: adminQuery.username,
      role: adminQuery.role,
      authorized_products: authorizedProducts,
      expires_in: 86400
    });

  } catch (error) {
    console.error('登录错误:', error);
    return c.json({
      error: '登录失败，请稍后重试'
    }, 500);
  }
});

export default admin;
